import React from 'react';
import { Button } from '@/libs/ui/Button/Button';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Icon } from '@/libs/icons/Icon';
import { SavedItemType } from '@/types';
import { Tooltip } from '@mantine/core';
import { useRemoveSavedItem } from '@/libs/cart/hooks/useRemoveSavedItem';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';

interface SavedItemsPanelProps {
  savedItems: SavedItemType[];
  startOpen: boolean;
}

export const SavedItemsPanel = ({
  savedItems,
  startOpen,
}: SavedItemsPanelProps) => {
  const { mutate: handleDeleteFromSavedItems } = useRemoveSavedItem();
  const { addToCart } = useCartStore();

  const handleMoveToCart = (savedItem: SavedItemType) => {
    addToCart({
      offers: [
        {
          productOfferId: savedItem.productOffer.id,
          quantity: savedItem.quantity,
        },
      ],
      onError: (message: string) => {
        console.error('Failed to add item to cart:', message);
      },
    });

    handleDeleteFromSavedItems(savedItem.id);
  };

  const handleMoveAllToCart = () => {
    // TODO: Missing implementation on backend
  };

  return (
    <CollapsiblePanel
      key={startOpen ? 'open' : 'closed'}
      startOpen={startOpen}
      header={
        <div className="flex w-full items-center justify-between pr-16 pl-4">
          <h4 className="text-sm font-medium">
            Saved Items ({savedItems?.length || 0})
          </h4>
          <Button variant="unstyled" onClick={handleMoveAllToCart}>
            <span className="text-xs font-medium text-[#447bfd]">
              Move all to cart
            </span>
          </Button>
        </div>
      }
      content={
        <div className="bg-[#FAFAFA] p-4">
          <div className="border-1 border-black/[0.04] bg-[#fff] p-4">
            {savedItems?.map((savedItem) => (
              <div
                key={savedItem.id}
                className="mb-2 flex items-center justify-between border-b border-black/[0.04] pb-2 [&:last-child]:m-0 [&:last-child]:border-none [&:last-child]:p-0"
              >
                <div className="flex items-center gap-2">
                  <div className="flex h-5 w-7 items-center justify-center rounded-sm bg-[#E5FCFD] text-[10px] font-medium">
                    {savedItem.quantity}
                  </div>
                  <div className="relative h-12 w-12 rounded-md border-1 border-black/[0.04]">
                    <img
                      src={savedItem.productOffer.product.imageUrl || ''}
                      alt={savedItem.productOffer.product.name}
                      className="absolute inset-0 h-full w-full object-cover"
                    />
                  </div>
                  <div>
                    <h5 className="text-sm font-medium">
                      {savedItem.productOffer.product.name}
                    </h5>
                    <p className="text-xs">
                      <span className="mr-1 text-[#666666] line-through">
                        $
                        {savedItem.productOffer.clinicPrice ||
                          savedItem.productOffer.price}
                      </span>
                      <span className="font-medium text-[#333]">
                        $
                        {savedItem.productOffer.clinicPrice ||
                          savedItem.productOffer.price}
                      </span>
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <Tooltip label="Remove Item from saved items list">
                    <Button
                      variant="unstyled"
                      onClick={() => handleDeleteFromSavedItems(savedItem.id)}
                      arial-label="Remove Item from saved items list"
                    >
                      <Icon
                        name="trash"
                        color="#667085"
                        size="1rem"
                        aria-hidden
                      />
                    </Button>
                  </Tooltip>
                  <div className="h-4 w-px bg-[#D0D5DD]" />
                  <Tooltip label="Move Item to cart">
                    <Button
                      className="flex items-center gap-1"
                      arial-label="Move Item to cart"
                      onClick={() => handleMoveToCart(savedItem)}
                    >
                      <Icon name="cart" color="#667085" size="1.25rem" />
                      <Icon name="plus" color="#667085" size="0.875rem" />
                    </Button>
                  </Tooltip>
                </div>
              </div>
            ))}
          </div>
        </div>
      }
    />
  );
};
