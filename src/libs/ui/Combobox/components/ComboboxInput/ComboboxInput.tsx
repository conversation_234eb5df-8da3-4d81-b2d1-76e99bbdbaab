import {
  useRef,
  type ChangeEvent,
  type KeyboardEvent,
  type ReactNode,
} from 'react';
import { Loader } from '@mantine/core';
import { Input } from '../../../../form/Input';
import { useComboboxContext } from '../../ComboboxContext';

interface ComboboxInputProps {
  placeholder?: string;
  className?: string;
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
  onEnterPress?: (value: string) => void;
  displayValue?: (value: unknown) => string;
  label?: string | ReactNode;
  error?: string;
  size?: 'sm' | 'md' | 'lg';
  align?: 'left' | 'center' | 'right';
  mask?: (value: string) => string;
  tooltip?: string;
}

export function ComboboxInput({
  placeholder,
  className,
  onChange,
  onEnterPress,
  displayValue,
  label,
  error,
  size,
  align,
  mask,
  tooltip,
}: ComboboxInputProps) {
  const {
    isOpen,
    selectedValue,
    inputValue,
    openCombobox,
    setInputValue,
    closeCombobox,
    isLoading,
    navigateOptions,
    selectHighlighted,
    resetHighlight,
    highlightedIndex,
  } = useComboboxContext();

  const inputRef = useRef<HTMLInputElement>(null);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setInputValue(newValue);
    onChange?.(event);
    if (!isOpen) {
      openCombobox();
    }
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
    switch (event.key) {
      case 'Enter':
        event.preventDefault();
        if (isOpen && highlightedIndex >= 0) {
          selectHighlighted();
        } else {
          const currentValue = inputValue.trim();
          if (currentValue && onEnterPress) {
            onEnterPress(currentValue);
          }
        }
        closeCombobox();
        break;

      case 'ArrowDown':
        event.preventDefault();
        navigateOptions('down');
        break;

      case 'ArrowUp':
        navigateOptions('up');
        break;

      case 'Escape':
        event.preventDefault();
        closeCombobox();
        resetHighlight();
        break;
    }
  };

  const handleFocus = () => {
    openCombobox();
  };

  const displayedValue = (() => {
    if (isOpen && inputValue.trim()) {
      return inputValue;
    }

    if (selectedValue && displayValue) {
      return displayValue(selectedValue);
    }

    return inputValue;
  })();

  return (
    <Input
      ref={inputRef}
      type="text"
      value={displayedValue}
      placeholder={placeholder}
      className="bg-amber-200"
      onChange={handleChange}
      onKeyDown={handleKeyDown}
      onFocus={handleFocus}
      label={label}
      error={error}
      size={size}
      align={align}
      mask={mask}
      tooltip={tooltip}
      rightSection={isLoading ? <Loader size="sm" /> : undefined}
    />
  );
}
