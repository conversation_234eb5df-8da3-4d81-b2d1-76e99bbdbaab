import { useProductStore } from '@/apps/shop/stores/useProductStore/useProductStore';

import { StockStatusIcon } from '@/libs/products/components/StockStatusIcon/StockStatusIcon';
import { LastOrderedOn } from '@/libs/ui/LastOrderedOn/LastOrderedOn';
import { useProductSuggestions } from '../useProductSuggestions';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { Link } from 'react-router-dom';

export const PreviouslyOrdered = () => {
  const { query } = useProductStore();
  const { previouslyOrderedItems } = useProductSuggestions(query);

  return (
    <>
      {previouslyOrderedItems.map((item) => (
        <div
          key={item.productOfferId}
          className="grid w-full grid-cols-[auto_1fr_auto] items-center rounded-[4px] border-[0.5px] border-gray-100 bg-gray-50 p-2 hover:border-[#0072C6] hover:bg-white"
        >
          <div className="mr-8">
            <Link
              to={getProductUrl(item.productId, item.productOfferId)}
              className="hover:underline"
            >
              <p className="mb-1 max-w-96 text-xs font-medium text-wrap text-black">
                {item.productName}
              </p>
            </Link>
            <div className="text-sxs flex items-center gap-1">
              <div className="[&_svg]:w-5">
                <StockStatusIcon status={'IN_STOCK'} />
              </div>
              <div className="divider-v"></div>
              <span className="text-sxs text-xs text-black">
                {item.vendorName}
              </span>
              <div className="divider-v"></div>
              <div className="[&_svg]:w-3">
                <LastOrderedOn
                  lastOrderedQuantity={item.quantity}
                  lastOrderedAt={item.lastOrderedAt}
                  size="sxs"
                />
              </div>
            </div>
          </div>
        </div>
      ))}
    </>
  );
};
