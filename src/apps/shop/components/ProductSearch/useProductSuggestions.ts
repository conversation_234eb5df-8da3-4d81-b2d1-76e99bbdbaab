import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useClinicStore } from '../../stores/useClinicStore';
import { useDebounce } from '@/libs/utils/hooks/useDebounce/useDebounce';
import { queryKeys } from '@/libs/query/queryClient';
import { get } from '@/libs/utils/api';

type OrderedProduct = {
  productOfferId: string;
  productId: string;
  productName: string;
  vendorId: string;
  vendorName: string;
  lastOrderedAt: string;
  quantity: number;
  price: number;
  imageUrl: string;
  orderCount: number;
};

export const useProductSuggestions = (
  query = '',
): {
  suggestions: string[];
  previouslyOrderedItems: OrderedProduct[];
  isSuggestionsLoading: boolean;
} => {
  const search = query.trim().toLowerCase();
  const [lastQueryWithNoResults, setLastEmptyQuery] = useState<string>('');
  const { clinic } = useClinicStore();
  const debouncedQuery = useDebounce(search, 300);

  const queryKey = queryKeys.products.suggestions(
    clinic?.id || '',
    debouncedQuery,
  );

  const shouldSkipQuery = () => {
    if (debouncedQuery?.length <= 2 || !clinic?.id) {
      return true;
    }

    const noResultsQuery =
      lastQueryWithNoResults &&
      debouncedQuery.startsWith(lastQueryWithNoResults);

    return noResultsQuery;
  };

  const {
    data = { suggestions: [], previouslyOrderedItems: [] },
    isLoading: isSuggestionsLoading,
  } = useQuery({
    queryKey,
    queryFn: async () => {
      try {
        const response = await get<{
          data: string[];
          previouslyOrderedItems: OrderedProduct[];
        }>({
          url: `/clinics/${clinic?.id}/autocomplete?query=${encodeURIComponent(debouncedQuery)}&clinicId=${clinic?.id}`,
        });

        const suggestions = response?.data || [];

        if (suggestions.length === 0) {
          setLastEmptyQuery(debouncedQuery);
        } else {
          setLastEmptyQuery('');
        }

        return {
          suggestions,
          previouslyOrderedItems: [
            {
              productOfferId: '9d757019-7202-48c6-ac43-5f792582af68',
              productId: '85009b46-2e26-4be2-b078-cfaa9997f550',
              productName:
                'Clavamox (Amoxicillin / Clavulanate) Drops for Veterinary Oral Suspension, 15mL',
              vendorId: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
              vendorName: 'Zoetis',
              lastOrderedAt: '2025-08-15T14:55:27+00:00',
              quantity: 3,
              price: 2130,
              imageUrl:
                'https://ws.mwiah.com/media/image?id=ec8dd978-232f-4b50-bd0a-f6dca46574d7',
              orderCount: 3,
            },
            {
              productOfferId: '9d7587b3-1568-4e71-a3af-c4bfc395ca9b',
              productId: '9ed26eba-06c9-46db-a7fe-b247d2a7e57c',
              productName: 'VetCollect Tubes',
              vendorId: '9d7559b3-3e3a-4e80-ae08-7118a8a8430b',
              vendorName: 'Zoetis',
              lastOrderedAt: '2025-08-15T14:55:27+00:00',
              quantity: 2,
              price: 6895,
              imageUrl:
                'https://static.idexx.com/product-images/en_us/98-09459-01/98-09459-01-piclarge.png',
              orderCount: 2,
            },
            {
              productOfferId: '9d7587b3-1568-4e71-a3af-c4bfc395ca9b',
              productId: '9ed26eba-06c9-46db-a7fe-b247d2a7e57c',
              productName: 'VetCollect Tubes',
              vendorId: '9d7559b3-3e3a-4e80-ae08-7118a8a8430b',
              vendorName: 'Zoetis',
              lastOrderedAt: '2025-08-15T14:55:27+00:00',
              quantity: 2,
              price: 6895,
              imageUrl:
                'https://static.idexx.com/product-images/en_us/98-09459-01/98-09459-01-piclarge.png',
              orderCount: 2,
            },
          ],
        };
      } catch (error) {
        setLastEmptyQuery(debouncedQuery);
        throw error;
      }
    },
    enabled: !shouldSkipQuery(),
    staleTime: 5 * 60 * 1000,
  });

  useEffect(() => {
    const isBackSpacing =
      lastQueryWithNoResults &&
      debouncedQuery.length < lastQueryWithNoResults.length;

    if (isBackSpacing) {
      setLastEmptyQuery('');
    }
  }, [debouncedQuery, lastQueryWithNoResults]);

  return {
    ...data,
    isSuggestionsLoading,
  };
};
