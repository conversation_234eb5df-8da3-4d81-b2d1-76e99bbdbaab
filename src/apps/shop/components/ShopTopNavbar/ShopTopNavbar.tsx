import { ProductSearchInput } from '@/apps/shop/components/ProductSearch/ProductSearch';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { useNavigate } from 'react-router-dom';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { Button } from '@/components';
import { getPriceString } from '@/utils';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { TopNavbar } from '@/libs/ui/TopNavbar/TopNavbar';
import { SwapClinic } from '@/libs/clinics/components/SwapClinic/SwapClinic';
import { Icon } from '@/libs/icons/Icon';

interface ShopTopNavbarProps {
  showCart?: boolean;
}
export const ShopTopNavbar = ({ showCart = true }: ShopTopNavbarProps) => {
  const navigate = useNavigate();
  // TODO: Add state for cart loading on store
  const { isCartLoading, subtotal, uniqueItemsCount } = useCartStore();
  const { clinic } = useClinicStore();
  const { account } = useAccountStore();

  const handleGoToCart = () => {
    navigate(SHOP_ROUTES_PATH.cart);
  };

  if (!clinic || !account) {
    return null;
  }

  return (
    <TopNavbar>
      <SwapClinic current={clinic} list={account.clinics} />
      <div className="flex w-full justify-between gap-4">
        <ProductSearchInput />
        {showCart ? (
          <div className="flex items-center gap-4 rounded-lg border border-gray-200">
            <div className="flex gap-2 pl-4">
              <p className="text-md font-bold">{getPriceString(subtotal)}</p>
              <div className="divider-v"></div>
              <p className="font-bold">{uniqueItemsCount ?? 0}</p>
            </div>
            <Button
              p="0"
              w="40"
              h="40"
              onClick={handleGoToCart}
              loading={isCartLoading}
            >
              <Icon name="cartSummary" size={'1.3rem'} />
            </Button>
          </div>
        ) : null}
      </div>
    </TopNavbar>
  );
};
